'use client';

import React, { useState, useCallback, useEffect, useRef } from 'react';
import TextEditor from '@/components/TextEditor';
import ActionButtonGroup from '@/components/pages/ActionButtonGroup';

/**
 * IslandInput Component
 *
 * Integrated with TextEditor component following BookingDetailsText pattern
 *
 * API Endpoints Used:
 * - GET /api/pages - Fetch all page data including island section
 * - POST /api/pages - Update specific page section (island)
 * - PATCH /api/pages - Update multiple sections at once
 *
 * Features:
 * - Rich text editing with TextEditor component
 * - Image upload functionality
 * - Sample content loading
 * - Direct API submission
 * - Enhanced error handling and user feedback
 * - Line height customization
 * - Text selection controls
 */

const IslandInput = ({
  formData,
  errors,
  onQuillChange,
  onSectionSave,
  isLoading
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [dataError, setDataError] = useState(null);
  const [localFormData, setLocalFormData] = useState({
    title: '',
    body1: '',
    image: ''
  });

  // Image upload state
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState('');
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const fileInputRef = useRef(null);

  // Centralized line height state management
  const [lineHeightState, setLineHeightState] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('islandLineHeight');
      return {
        global: saved ? parseFloat(saved) : 1.6,
        current: saved ? parseFloat(saved) : 1.6,
        hasSelection: false
      };
    }
    return {
      global: 1.6,
      current: 1.6,
      hasSelection: false
    };
  });

  const [selectedText, setSelectedText] = useState('');
  const [showSelectionControls, setShowSelectionControls] = useState(false);
  const editorRef = useRef(null);

  // Available line height options for validation
  const availableLineHeights = [0.4, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 1.8, 2.0, 2.2, 2.4];

  // Find the nearest available line height option
  const findNearestAvailableLineHeight = useCallback((value) => {
    return availableLineHeights.reduce((prev, curr) =>
      Math.abs(curr - value) < Math.abs(prev - value) ? curr : prev
    );
  }, [availableLineHeights]);

  // Find the most common line height from an array
  const findMostCommonLineHeight = useCallback((lineHeights) => {
    if (lineHeights.length === 1) return lineHeights[0];

    const frequency = {};
    lineHeights.forEach(lh => {
      frequency[lh] = (frequency[lh] || 0) + 1;
    });

    const mostCommon = Object.keys(frequency).reduce((a, b) =>
      frequency[a] > frequency[b] ? a : b
    );

    return parseFloat(mostCommon);
  }, []);

  // Validate and normalize detected line height
  const validateLineHeight = useCallback((detectedValue) => {
    const numValue = parseFloat(detectedValue);
    if (isNaN(numValue)) {
      return lineHeightState.global;
    }

    if (availableLineHeights.includes(numValue)) {
      return numValue;
    }

    const closest = findNearestAvailableLineHeight(numValue);
    console.log(`📐 Normalized line height ${numValue} to closest available option: ${closest}`);
    return closest;
  }, [lineHeightState.global, availableLineHeights, findNearestAvailableLineHeight]);

  // Extract line height from a single element
  const extractLineHeightFromElement = useCallback((element, contentEditable) => {
    if (!element || element === contentEditable) return null;

    if (element.style && element.style.lineHeight) {
      const inlineValue = parseFloat(element.style.lineHeight);
      if (!isNaN(inlineValue) && inlineValue >= 0.1 && inlineValue <= 10) {
        return inlineValue;
      }
    }

    try {
      const computedStyle = window.getComputedStyle(element);
      const computedLineHeight = computedStyle.lineHeight;

      if (computedLineHeight && computedLineHeight !== 'normal' && computedLineHeight !== 'inherit') {
        if (computedLineHeight.endsWith('px')) {
          const fontSize = parseFloat(computedStyle.fontSize);
          if (fontSize > 0) {
            const pixelLineHeight = parseFloat(computedLineHeight);
            const relativeLineHeight = pixelLineHeight / fontSize;
            if (!isNaN(relativeLineHeight) && relativeLineHeight >= 0.1 && relativeLineHeight <= 10) {
              return findNearestAvailableLineHeight(relativeLineHeight);
            }
          }
        } else {
          const numericValue = parseFloat(computedLineHeight);
          if (!isNaN(numericValue) && numericValue >= 0.1 && numericValue <= 10) {
            return findNearestAvailableLineHeight(numericValue);
          }
        }
      }
    } catch (error) {
      console.warn('Error getting computed style for element:', error);
    }

    return null;
  }, [findNearestAvailableLineHeight]);

  // Robust line height detection for selected text
  const detectSelectionLineHeight = useCallback((selection) => {
    if (!selection || selection.rangeCount === 0) {
      return lineHeightState.global;
    }

    const range = selection.getRangeAt(0);
    const contentEditable = editorRef.current?.querySelector('[contenteditable="true"]');

    const lineHeights = [];
    const selectedElements = [];

    if (range.commonAncestorContainer) {
      const walker = document.createTreeWalker(
        range.commonAncestorContainer,
        NodeFilter.SHOW_ELEMENT,
        {
          acceptNode: (node) => {
            const nodeRange = document.createRange();
            try {
              nodeRange.selectNodeContents(node);
              return (range.compareBoundaryPoints(Range.START_TO_END, nodeRange) >= 0 &&
                      range.compareBoundaryPoints(Range.END_TO_START, nodeRange) <= 0)
                      ? NodeFilter.FILTER_ACCEPT
                      : NodeFilter.FILTER_SKIP;
            } catch (e) {
              return NodeFilter.FILTER_SKIP;
            }
          }
        }
      );

      let node;
      while (node = walker.nextNode()) {
        if (node !== contentEditable && !contentEditable?.contains(node)) continue;
        selectedElements.push(node);
      }
    }

    let startElement = range.startContainer;
    let endElement = range.endContainer;

    if (startElement.nodeType === Node.TEXT_NODE) {
      startElement = startElement.parentElement;
    }
    if (endElement.nodeType === Node.TEXT_NODE) {
      endElement = endElement.parentElement;
    }

    const elementsToCheck = [...selectedElements, startElement, endElement];
    const uniqueElements = [...new Set(elementsToCheck.filter(el => el && el !== contentEditable))];

    console.log('🔍 Checking line height on elements:', uniqueElements.length, 'elements');

    for (const element of uniqueElements) {
      const lineHeight = extractLineHeightFromElement(element, contentEditable);
      if (lineHeight !== null) {
        lineHeights.push(lineHeight);
        console.log('📏 Found line height:', lineHeight, 'on element:', element.tagName, element.className);
      }
    }

    if (lineHeights.length === 0) {
      console.log('⚠️ No line heights detected, using global default:', lineHeightState.global);
      return lineHeightState.global;
    }

    const mostCommon = findMostCommonLineHeight(lineHeights);
    const validated = validateLineHeight(mostCommon);
    console.log('✅ Selected line height:', validated, 'from detected values:', lineHeights, 'most common:', mostCommon);

    return validated;
  }, [lineHeightState.global, extractLineHeightFromElement, findMostCommonLineHeight, validateLineHeight]);

  // Update local form data when formData changes
  useEffect(() => {
    if (formData) {
      setLocalFormData({
        title: formData.title || '',
        body1: formData.body1 || '',
        image: formData.image || ''
      });
      setImagePreview(formData.image || '');
    }
  }, [formData]);

  // Attach selection listeners to TextEditor's contentEditable element
  useEffect(() => {
    if (isEditing && editorRef.current) {
      const findContentEditable = () => {
        const contentEditable = editorRef.current?.querySelector('[contenteditable="true"]');
        return contentEditable;
      };

      const handleSelection = () => {
        try {
          const selection = window.getSelection();
          if (selection && selection.toString().trim()) {
            setSelectedText(selection.toString());
            setShowSelectionControls(true);

            if (selection.rangeCount > 0) {
              const detectedLineHeight = detectSelectionLineHeight(selection);

              console.log('🎯 WYSIWYG Line Height Detection:', {
                selectedText: selection.toString().substring(0, 50),
                detectedValue: detectedLineHeight,
                previousValue: lineHeightState.current,
                availableOptions: availableLineHeights
              });

              setLineHeightState(prev => ({
                ...prev,
                current: detectedLineHeight,
                hasSelection: true
              }));
            }
          } else {
            setSelectedText('');
            setShowSelectionControls(false);
            setLineHeightState(prev => ({
              ...prev,
              current: prev.global,
              hasSelection: false
            }));
          }
        } catch (error) {
          console.error('Error in selection handler:', error);
          setSelectedText('');
          setShowSelectionControls(false);
        }
      };

      const timeoutId = setTimeout(() => {
        const contentEditable = findContentEditable();
        if (contentEditable) {
          contentEditable.addEventListener('mouseup', handleSelection);
          contentEditable.addEventListener('keyup', handleSelection);
          contentEditable.addEventListener('focus', handleSelection);
        }
      }, 100);

      return () => {
        clearTimeout(timeoutId);
        const contentEditable = findContentEditable();
        if (contentEditable) {
          contentEditable.removeEventListener('mouseup', handleSelection);
          contentEditable.removeEventListener('keyup', handleSelection);
          contentEditable.removeEventListener('focus', handleSelection);
        }
      };
    }
  }, [isEditing, lineHeightState.global, detectSelectionLineHeight]);

  // Clean up selection state when exiting edit mode
  useEffect(() => {
    if (!isEditing) {
      setSelectedText('');
      setShowSelectionControls(false);
      setLineHeightState(prev => ({
        ...prev,
        current: prev.global,
        hasSelection: false
      }));
    }
  }, [isEditing]);

  // Handle local form changes when in edit mode
  const handleLocalChange = useCallback((content, field) => {
    setLocalFormData(prev => ({
      ...prev,
      [field]: content
    }));
  }, []);

  // Handle image file selection
  const handleImageSelect = useCallback((event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        setDataError('Please select a valid image file (JPEG, PNG, GIF, or WebP)');
        return;
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB in bytes
      if (file.size > maxSize) {
        setDataError('Image file size must be less than 10MB');
        return;
      }

      setImageFile(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
        handleLocalChange(e.target.result, 'image');
      };
      reader.readAsDataURL(file);

      setDataError(null);
    }
  }, [handleLocalChange]);

  // Handle image upload to Firebase
  const uploadImageToFirebase = useCallback(async (file) => {
    if (!file) return null;

    setIsUploadingImage(true);
    const formData = new FormData();
    formData.append('file', file);
    formData.append('folder', 'island');

    try {
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        return result.url;
      } else {
        throw new Error(result.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    } finally {
      setIsUploadingImage(false);
    }
  }, []);

  // Handle loading sample content
  const handleLoadSampleContent = useCallback(() => {
    const sampleData = {
      title: 'Welcome to Elephant Island',
      body1: `<p><strong>Discover Paradise</strong></p><p><em>Experience the ultimate adventure</em> on our <span style="color: rgb(0, 123, 255);">pristine island sanctuary</span>.</p><p>Our island offers:</p><p>• <strong>Breathtaking landscapes</strong> with untouched natural beauty</p><p>• <strong>Rich wildlife</strong> including unique species found nowhere else</p><p>• <strong>Adventure activities</strong> for all skill levels</p><p>• <strong>Peaceful retreats</strong> for relaxation and reflection</p><p>Contact us at <a href="mailto:<EMAIL>" style="color: blue; text-decoration: underline;"><EMAIL></a> for more information or visit our <a href="https://elephantisland.com" style="color: blue; text-decoration: underline;">main website</a>.</p><p>This magnificent island provides an unforgettable experience where nature and adventure meet. Select any text above to customize formatting, adjust line heights, or convert text to links.</p>`,
      image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80'
    };

    setLocalFormData(sampleData);
    setImagePreview(sampleData.image);
  }, []);

  // Handle clearing content
  const handleClearContent = useCallback(() => {
    setLocalFormData({
      title: '',
      body1: '',
      image: ''
    });
    setImagePreview('');
    setImageFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  // Helper function to show success messages
  const showSuccessMessage = useCallback((message) => {
    const successMessage = document.createElement('div');
    successMessage.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg z-50 flex items-center space-x-2';
    successMessage.innerHTML = `
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
      </svg>
      <span>${message}</span>
    `;
    document.body.appendChild(successMessage);
    setTimeout(() => {
      if (document.body.contains(successMessage)) {
        document.body.removeChild(successMessage);
      }
    }, 3000);
  }, []);

  // Helper function to show error messages
  const showErrorMessage = useCallback((message) => {
    const errorMessage = document.createElement('div');
    errorMessage.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-md shadow-lg z-50 flex items-center space-x-2';
    errorMessage.innerHTML = `
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
      <span>${message}</span>
    `;
    document.body.appendChild(errorMessage);
    setTimeout(() => {
      if (document.body.contains(errorMessage)) {
        document.body.removeChild(errorMessage);
      }
    }, 5000);
  }, []);

  // Enhanced validation function
  const validateIslandData = useCallback((data) => {
    const errors = [];

    if (!data.title || !data.title.trim()) {
      errors.push('Island title cannot be empty');
    }

    if (data.title && data.title.trim().length < 3) {
      errors.push('Island title must be at least 3 characters long');
    }

    if (data.title && data.title.trim().length > 200) {
      errors.push('Island title cannot exceed 200 characters');
    }

    if (!data.body1 || !data.body1.trim()) {
      errors.push('Island description cannot be empty');
    }

    if (data.body1 && data.body1.trim().length < 10) {
      errors.push('Island description must be at least 10 characters long');
    }

    if (data.body1 && data.body1.trim().length > 5000) {
      errors.push('Island description cannot exceed 5000 characters');
    }

    return errors;
  }, []);

  // Direct API submission function for island data
  const submitIslandDataDirectly = useCallback(async (islandData) => {
    try {
      console.log('🚀 Submitting island data directly to API:', islandData);

      const response = await fetch('/api/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: 'island',
          data: islandData
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        console.log('✅ Island data submitted successfully:', result);
        return result;
      } else {
        throw new Error(result.message || 'Failed to submit island data');
      }
    } catch (error) {
      console.error('❌ Error submitting island data:', error);
      throw error;
    }
  }, []);

  // Apply line height to selected text
  const applyLineHeightToSelection = useCallback((newLineHeight) => {
    const selection = window.getSelection();

    if (selection && selection.rangeCount > 0 && !selection.isCollapsed) {
      const range = selection.getRangeAt(0);

      try {
        const span = document.createElement('span');
        span.style.lineHeight = newLineHeight.toString();
        span.style.display = 'inline-block';

        try {
          range.surroundContents(span);
        } catch (error) {
          const contents = range.extractContents();
          span.appendChild(contents);
          range.insertNode(span);
        }

        if (editorRef.current) {
          const contentEditable = editorRef.current.querySelector('[contenteditable="true"]');
          if (contentEditable) {
            const newContent = contentEditable.innerHTML;
            handleLocalChange(newContent, 'body1');
          }
        }

        selection.removeAllRanges();
        setShowSelectionControls(false);
        setSelectedText('');
        setLineHeightState(prev => ({
          ...prev,
          hasSelection: false,
          current: prev.global
        }));

      } catch (error) {
        console.error('Failed to apply line height to selection:', error);
      }
    }
  }, [handleLocalChange]);

  // Centralized line height change handler
  const handleLineHeightChange = useCallback((value, applyToSelection = false) => {
    const newLineHeight = parseFloat(value);
    console.log('🔧 Line height change:', { value, newLineHeight, applyToSelection, hasSelection: lineHeightState.hasSelection });

    if (applyToSelection && lineHeightState.hasSelection) {
      console.log('📝 Applying to selection:', newLineHeight);
      applyLineHeightToSelection(newLineHeight);

      setLineHeightState(prev => ({
        ...prev,
        current: newLineHeight
      }));
    } else {
      console.log('🌐 Updating global line height:', newLineHeight);
      setLineHeightState(prev => ({
        ...prev,
        global: newLineHeight,
        current: prev.hasSelection ? prev.current : newLineHeight
      }));

      if (typeof window !== 'undefined') {
        localStorage.setItem('islandLineHeight', newLineHeight.toString());
      }
    }
  }, [lineHeightState.hasSelection, applyLineHeightToSelection]);

  // Clear all line height formatting
  const clearAllLineHeightFormatting = useCallback(() => {
    if (editorRef.current) {
      const contentEditable = editorRef.current.querySelector('[contenteditable="true"]');
      if (contentEditable) {
        const content = contentEditable.innerHTML;
        const cleanedContent = content.replace(/style="[^"]*line-height:[^;"]*;?[^"]*"/g, (match) => {
          const styleContent = match.match(/style="([^"]*)"/)[1];
          const cleanedStyle = styleContent
            .split(';')
            .filter(style => !style.trim().startsWith('line-height') && !style.trim().startsWith('display'))
            .join(';');
          return cleanedStyle ? `style="${cleanedStyle}"` : '';
        });

        handleLocalChange(cleanedContent, 'body1');
      }
    }
  }, [handleLocalChange]);

  // Fetch fresh data when entering edit mode with enhanced error handling
  const fetchLatestData = useCallback(async () => {
    setIsLoadingData(true);
    setDataError(null);

    try {
      const response = await fetch('/api/pages', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        const islandData = data.data?.island || {};
        const freshFormData = {
          title: islandData.title || '',
          body1: islandData.body1 || '',
          image: islandData.image || ''
        };
        setLocalFormData(freshFormData);
        setImagePreview(freshFormData.image);
        console.log('✅ Successfully fetched latest island data');
      } else {
        throw new Error(data.message || 'Failed to fetch latest data');
      }
    } catch (error) {
      console.error('❌ Error fetching latest island data:', error);
      setDataError(`Failed to load latest data: ${error.message}. Using current values.`);
      const currentFormData = {
        title: formData?.title || '',
        body1: formData?.body1 || '',
        image: formData?.image || ''
      };
      setLocalFormData(currentFormData);
      setImagePreview(currentFormData.image);
    } finally {
      setIsLoadingData(false);
    }
  }, [formData]);

  // Handle edit mode toggle
  const handleEdit = useCallback(async () => {
    setIsEditing(true);
    await fetchLatestData();
  }, [fetchLatestData]);

  // Handle save changes with enhanced error handling and API integration
  const handleSave = useCallback(async () => {
    try {
      // Upload image if there's a new file
      let finalImageUrl = localFormData.image;
      if (imageFile) {
        try {
          finalImageUrl = await uploadImageToFirebase(imageFile);
          setLocalFormData(prev => ({ ...prev, image: finalImageUrl }));
          setImagePreview(finalImageUrl);
        } catch (uploadError) {
          console.error('Image upload failed:', uploadError);
          setDataError(`Image upload failed: ${uploadError.message}`);
          showErrorMessage(`Image upload failed: ${uploadError.message}`);
          return;
        }
      }

      const dataToValidate = { ...localFormData, image: finalImageUrl };
      const validationErrors = validateIslandData(dataToValidate);
      if (validationErrors.length > 0) {
        const errorMessage = validationErrors.join(', ');
        setDataError(errorMessage);
        showErrorMessage(errorMessage);
        return;
      }

      setDataError(null);

      // Update the parent form data first
      Object.keys(dataToValidate).forEach(field => {
        onQuillChange(dataToValidate[field], 'island', field);
      });

      await new Promise(resolve => setTimeout(resolve, 100));

      try {
        await onSectionSave('island', dataToValidate);
        setIsEditing(false);
        setImageFile(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        showSuccessMessage('Island data saved successfully!');
      } catch (apiError) {
        console.error('API Error saving island:', apiError);
        const errorMsg = `Failed to save changes: ${apiError.message || 'Unknown error occurred'}`;
        setDataError(errorMsg);
        showErrorMessage(errorMsg);
      }
    } catch (error) {
      console.error('Error saving island:', error);
      const errorMsg = `Failed to save changes: ${error.message || 'Please try again.'}`;
      setDataError(errorMsg);
      showErrorMessage(errorMsg);
    }
  }, [localFormData, imageFile, uploadImageToFirebase, validateIslandData, onQuillChange, onSectionSave, showSuccessMessage, showErrorMessage]);

  // Handle direct save to API (bypassing parent form)
  const handleDirectSave = useCallback(async () => {
    try {
      // Upload image if there's a new file
      let finalImageUrl = localFormData.image;
      if (imageFile) {
        try {
          finalImageUrl = await uploadImageToFirebase(imageFile);
          setLocalFormData(prev => ({ ...prev, image: finalImageUrl }));
          setImagePreview(finalImageUrl);
        } catch (uploadError) {
          console.error('Image upload failed:', uploadError);
          setDataError(`Image upload failed: ${uploadError.message}`);
          showErrorMessage(`Image upload failed: ${uploadError.message}`);
          return;
        }
      }

      const dataToValidate = { ...localFormData, image: finalImageUrl };
      const validationErrors = validateIslandData(dataToValidate);
      if (validationErrors.length > 0) {
        const errorMessage = validationErrors.join(', ');
        setDataError(errorMessage);
        showErrorMessage(errorMessage);
        return;
      }

      setDataError(null);

      await submitIslandDataDirectly(dataToValidate);
      setIsEditing(false);
      setImageFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      showSuccessMessage('Island data saved directly to database!');

    } catch (error) {
      console.error('Error saving island directly:', error);
      const errorMsg = `Failed to save changes: ${error.message || 'Please try again.'}`;
      setDataError(errorMsg);
      showErrorMessage(errorMsg);
    }
  }, [localFormData, imageFile, uploadImageToFirebase, validateIslandData, submitIslandDataDirectly, showSuccessMessage, showErrorMessage]);

  // Handle cancel edit
  const handleCancel = useCallback(() => {
    // Reset to current form data when canceling
    setLocalFormData({
      title: formData?.title || '',
      body1: formData?.body1 || '',
      image: formData?.image || ''
    });
    setImagePreview(formData?.image || '');
    setImageFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    setIsEditing(false);
  }, [formData]);

  // Handle delete
  const handleDelete = useCallback(async () => {
    try {
      // Clear all fields
      const emptyData = {
        title: '',
        body1: '',
        image: ''
      };

      Object.keys(emptyData).forEach(field => {
        onQuillChange(emptyData[field], 'island', field);
      });

      await onSectionSave();
      setShowDeleteConfirm(false);
      setIsEditing(false);
      setImagePreview('');
      setImageFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error deleting island:', error);
    }
  }, [onQuillChange, onSectionSave]);

  const currentData = isEditing ? localFormData : formData;
  const hasContent = formData.title || formData.body1 || formData.image;

  return (
    <>
      {/* CSS for preserving line heights */}
      <style jsx>{`
        .preserve-line-heights span[style*="line-height"] {
          display: inline-block;
        }
        .selection-highlight {
          background-color: rgba(59, 130, 246, 0.1);
          border-radius: 2px;
          padding: 1px 2px;
        }
      `}</style>

    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Island Content</h3>

        <ActionButtonGroup
          mode={isEditing ? 'edit' : 'view'}
          isLoading={isLoadingData || isLoading || isUploadingImage}
          hasContent={hasContent}
          onEdit={handleEdit}
          onSave={handleSave}
          onCancel={handleCancel}
          onDelete={() => setShowDeleteConfirm(true)}
          onDirectSave={handleDirectSave}
          onLoadSample={handleLoadSampleContent}
          showDirectSave={true}
          showLoadSample={true}
          showDelete={true}
          responsive={true}
          customButtons={[
            {
              mode: 'view',
              variant: 'success',
              onClick: onSectionSave,
              disabled: isLoading,
              loading: isLoading,
              loadingText: 'Saving...',
              label: 'Save Section'
            }
          ]}
        />
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <h3 className="text-lg font-medium text-gray-900">Confirm Delete</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete all Island content? This action cannot be undone.
                </p>
              </div>
              <div className="flex justify-center space-x-3 mt-4">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDelete}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Data Error Display */}
      {dataError && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-800">{dataError}</p>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {(isLoadingData || isUploadingImage) && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
            <p className="text-sm text-blue-800">
              {isUploadingImage ? 'Uploading image...' : 'Loading latest data...'}
            </p>
          </div>
        </div>
      )}

      {/* Content Display/Edit Form */}
      {!hasContent && !isEditing ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <p className="text-gray-500 mb-4">No island content available</p>
          <button
            onClick={handleEdit}
            disabled={isLoadingData}
            className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoadingData ? 'Loading...' : 'Create Content'}
          </button>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Island Title *
            </label>
            {isEditing ? (
              <input
                type="text"
                value={currentData.title}
                onChange={(e) => handleLocalChange(e.target.value, 'title')}
                placeholder="Enter island title"
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors['island.title'] ? 'border-red-500' : 'border-gray-300'
                }`}
                maxLength={200}
              />
            ) : (
              <div className="p-4 bg-gray-50 rounded-md border">
                <p className="text-gray-900">{currentData.title || 'No title set'}</p>
              </div>
            )}
            {errors['island.title'] && (
              <p className="mt-1 text-sm text-red-600">{errors['island.title']}</p>
            )}
          </div>

          {/* Image */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Island Image
            </label>
            {isEditing ? (
              <div className="space-y-4">
                {/* Image Preview */}
                {imagePreview && (
                  <div className="relative">
                    <img
                      src={imagePreview}
                      alt="Island preview"
                      className="w-full h-48 object-cover rounded-md border"
                    />
                    <button
                      type="button"
                      onClick={() => {
                        setImagePreview('');
                        handleLocalChange('', 'image');
                        setImageFile(null);
                        if (fileInputRef.current) {
                          fileInputRef.current.value = '';
                        }
                      }}
                      className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                )}

                {/* File Input */}
                <div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleImageSelect}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Supported formats: JPEG, PNG, GIF, WebP. Max size: 10MB
                  </p>
                </div>
              </div>
            ) : (
              <div className="p-4 bg-gray-50 rounded-md border">
                {currentData.image ? (
                  <img
                    src={currentData.image}
                    alt="Island"
                    className="w-full h-48 object-cover rounded-md"
                  />
                ) : (
                  <p className="text-gray-500">No image set</p>
                )}
              </div>
            )}
            {errors['island.image'] && (
              <p className="mt-1 text-sm text-red-600">{errors['island.image']}</p>
            )}
          </div>

          {/* Body1 - Description */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700">
                Island Description *
              </label>
              {isEditing && (
                <div className="flex items-center space-x-2">
                  <label className="text-xs text-gray-500">
                    {lineHeightState.hasSelection ? 'Selection' : 'Default'} Line Height:
                  </label>
                  <select
                    value={lineHeightState.current}
                    onChange={(e) => handleLineHeightChange(e.target.value, lineHeightState.hasSelection)}
                    className="text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  >
                    <option value="0.4">0.4</option>
                    <option value="0.6">0.6</option>
                    <option value="0.8">0.8</option>
                    <option value="1.0">1.0</option>
                    <option value="1.2">1.2</option>
                    <option value="1.4">1.4</option>
                    <option value="1.6">1.6</option>
                    <option value="1.8">1.8</option>
                    <option value="2.0">2.0</option>
                    <option value="2.2">2.2</option>
                    <option value="2.4">2.4</option>
                  </select>
                  {currentData.body1 && currentData.body1.includes('line-height') && (
                    <button
                      type="button"
                      onClick={clearAllLineHeightFormatting}
                      className="text-xs px-2 py-1 text-red-600 bg-red-50 border border-red-200 rounded hover:bg-red-100 focus:outline-none focus:ring-1 focus:ring-red-500"
                      title="Clear all custom line heights"
                    >
                      Clear All
                    </button>
                  )}
                </div>
              )}
            </div>
            {isEditing ? (
              <div className="space-y-3">
                {/* Content Control Buttons */}
                <div className="flex gap-2 mb-3">
                  <button
                    type="button"
                    onClick={handleLoadSampleContent}
                    className="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  >
                    Load Sample Content
                  </button>
                  <button
                    type="button"
                    onClick={handleClearContent}
                    className="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-1 focus:ring-gray-500"
                  >
                    Clear Content
                  </button>
                </div>

                {/* Selection indicator */}
                {showSelectionControls && selectedText && (
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-blue-800">
                        Selected: "{selectedText.length > 30 ? selectedText.substring(0, 30) + '...' : selectedText}"
                      </span>
                      <span className="text-xs text-blue-600">
                        (Line height: {lineHeightState.current})
                      </span>
                      <span className="text-xs text-gray-500">
                        • Use the line height control above to modify
                      </span>
                    </div>
                  </div>
                )}

                {/* Help text when no selection */}
                {!showSelectionControls && (
                  <div className="bg-gray-50 border border-gray-200 rounded-md p-2">
                    <p className="text-xs text-gray-600">
                      💡 Select text to apply custom line height to specific portions, or use the global setting above for new text. Use the buttons above to load sample content or clear the editor.
                    </p>
                  </div>
                )}

                <div
                  className={`${errors['island.body1'] ? 'border-red-500' : ''}`}
                  ref={editorRef}
                >
                  <TextEditor
                    key={`body1-edit-${isEditing}`}
                    value={currentData.body1}
                    onChange={(content) => handleLocalChange(content, 'body1')}
                    placeholder="Enter island description"
                    style={{ minHeight: '120px', lineHeight: lineHeightState.global }}
                    className={`border rounded-md ${errors['island.body1'] ? 'border-red-500' : 'border-gray-300'}`}
                  />
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">
                    Default line height: {lineHeightState.global}
                    {currentData.body1 && currentData.body1.includes('line-height') &&
                      <span className="ml-2 text-blue-600">• Contains custom line heights</span>
                    }
                  </span>
                </div>
                <div className="p-4 bg-gray-50 rounded-md border" style={{ lineHeight: lineHeightState.global }}>
                  <div
                    dangerouslySetInnerHTML={{ __html: currentData.body1 || 'No description set' }}
                    className="preserve-line-heights"
                  />
                </div>
              </div>
            )}
            {errors['island.body1'] && (
              <p className="mt-1 text-sm text-red-600">{errors['island.body1']}</p>
            )}
          </div>
        </div>
      )}
    </div>
    </>
  );
};

export default IslandInput;
