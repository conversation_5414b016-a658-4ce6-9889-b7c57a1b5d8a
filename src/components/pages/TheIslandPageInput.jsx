'use client';

import { useState, useEffect } from 'react';
import { MdSave, MdCancel, MdCloudUpload, MdEdit } from 'react-icons/md';
import TextEditor from '@/components/common/TextEditor';

// Quill editor configuration
const quillModules = {
  toolbar: [
    [{ 'header': [1, 2, 3, false] }],
    [{ 'size': ['small', false, 'large', 'huge'] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    [{ 'color': [] }, { 'background': [] }],
    [{ 'align': [] }],
    ['link'],
    ['clean']
  ],
};

const quillFormats = [
  'header', 'size', 'bold', 'italic', 'underline', 'strike',
  'list', 'bullet', 'color', 'background', 'align', 'link'
];

export default function TheIslandPageInput({
  islandData = null,
  onSave,
  onCancel,
  isLoading = false
}) {
  const [formData, setFormData] = useState({
    title: '',
    body1: '',
    image: '',
  });
  const [errors, setErrors] = useState({});
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState('');
  const [uploading, setUploading] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(false);

  // Initialize form data when islandData prop changes
  useEffect(() => {
    if (islandData) {
      setFormData({
        title: islandData.title || '',
        body1: islandData.body1 || '',
        image: islandData.image || '',
      });
      setImagePreview(islandData.image || '');
    } else {
      setFormData({
        title: '',
        body1: '',
        image: '',
      });
      setImagePreview('');
    }
  }, [islandData]);

  // Fetch latest island data from API
  const fetchLatestData = async () => {
    setIsLoadingData(true);
    try {
      const response = await fetch('/api/pages', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        const island = data.data?.island || {};
        setFormData({
          title: island.title || '',
          body1: island.body1 || '',
          image: island.image || '',
        });
        setImagePreview(island.image || '');
        console.log('✅ Successfully fetched latest island data');
      } else {
        throw new Error(data.message || 'Failed to fetch latest data');
      }
    } catch (error) {
      console.error('❌ Error fetching latest island data:', error);
      setErrors({ fetch: `Failed to load latest data: ${error.message}` });
    } finally {
      setIsLoadingData(false);
    }
  };

  // Handle entering edit mode
  const handleEdit = async () => {
    setIsEditMode(true);
    await fetchLatestData();
  };

  // Handle Quill editor content changes
  const handleQuillChange = (content, fieldName) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: content
    }));

    // Clear error when user starts typing
    if (errors[fieldName]) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: ''
      }));
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        setErrors(prev => ({ ...prev, image: 'Please select a valid image file (JPEG, PNG, GIF, or WebP)' }));
        return;
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB in bytes
      if (file.size > maxSize) {
        setErrors(prev => ({ ...prev, image: 'Image file size must be less than 10MB' }));
        return;
      }

      setImageFile(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);

      // Clear any existing image errors
      if (errors.image) {
        setErrors(prev => ({ ...prev, image: '' }));
      }
    }
  };

  const uploadImage = async () => {
    if (!imageFile) return null;

    setUploading(true);
    try {
      const formDataUpload = new FormData();
      formDataUpload.append('file', imageFile);
      formDataUpload.append('folder', 'island');

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formDataUpload,
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        return result.url;
      } else {
        throw new Error(result.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Image upload error:', error);
      throw error;
    } finally {
      setUploading(false);
    }
  };

  // Helper function to strip HTML tags and check if content is empty
  const isQuillContentEmpty = (content) => {
    if (!content) return true;
    // Remove HTML tags and check if there's actual text content
    const textContent = content.replace(/<[^>]*>/g, '').trim();
    return textContent === '' || textContent === '\n';
  };

  const validateForm = () => {
    const newErrors = {};

    if (isQuillContentEmpty(formData.title)) {
      newErrors.title = 'Title is required';
    } else if (formData.title.length > 200) {
      newErrors.title = 'Title cannot exceed 200 characters';
    }

    if (isQuillContentEmpty(formData.body1)) {
      newErrors.body1 = 'Description is required';
    } else if (formData.body1.length > 5000) {
      newErrors.body1 = 'Description cannot exceed 5000 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      let imageUrl = formData.image;
      
      // Upload new image if selected
      if (imageFile) {
        imageUrl = await uploadImage();
      }
      
      const submitData = {
        ...formData,
        image: imageUrl,
      };
      
      await onSave(submitData);
      setIsEditMode(false);
    } catch (error) {
      console.error('Form submission error:', error);
      setErrors({ submit: 'Failed to save island data. Please try again.' });
    }
  };

  const handleCancel = () => {
    // Reset form data to original values
    if (islandData) {
      setFormData({
        title: islandData.title || '',
        body1: islandData.body1 || '',
        image: islandData.image || '',
      });
      setImagePreview(islandData.image || '');
    }
    setImageFile(null);
    setErrors({});
    setIsEditMode(false);
    if (onCancel) onCancel();
  };

  const hasContent = formData.title || formData.body1 || formData.image;

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <style jsx>{`
        .ql-editor {
          min-height: 80px;
        }
        .ql-editor.ql-blank::before {
          font-style: normal;
          color: #9ca3af;
        }
        .ql-toolbar {
          border-top: 1px solid #d1d5db;
          border-left: 1px solid #d1d5db;
          border-right: 1px solid #d1d5db;
          border-bottom: none;
        }
        .ql-container {
          border-bottom: 1px solid #d1d5db;
          border-left: 1px solid #d1d5db;
          border-right: 1px solid #d1d5db;
          border-top: none;
        }
      `}</style>
      
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-900">
          Island Page Content
        </h2>
        
        {!isEditMode && hasContent && (
          <button
            onClick={handleEdit}
            disabled={isLoadingData}
            className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center"
          >
            <MdEdit className="mr-2" />
            {isLoadingData ? 'Loading...' : 'Edit'}
          </button>
        )}
      </div>

      {/* Loading State */}
      {isLoadingData && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
            <p className="text-sm text-blue-800">Loading latest data...</p>
          </div>
        </div>
      )}

      {/* Fetch Error */}
      {errors.fetch && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
          <p className="text-sm text-yellow-800">{errors.fetch}</p>
        </div>
      )}

      {/* Content Display/Edit Form */}
      {!hasContent && !isEditMode ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <p className="text-gray-500 mb-4">No island content available</p>
          <button
            onClick={handleEdit}
            disabled={isLoadingData}
            className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoadingData ? 'Loading...' : 'Create Content'}
          </button>
        </div>
      ) : isEditMode ? (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className='flex flex-col w-full gap-4'>
            {/* Title */}
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                Island Title *
              </label>
              <div className={`${errors.title ? 'border-red-500' : ''}`}>
                <TextEditor
                  theme="snow"
                  value={formData.title}
                  onChange={(content) => handleQuillChange(content, 'title')}
                  modules={quillModules}
                  formats={quillFormats}
                  placeholder="Enter island title"
                  style={{ minHeight: '80px' }}
                  className={`border rounded-md ${errors.title ? 'border-red-500' : 'border-gray-300'}`}
                />
              </div>
              {errors.title && (
                <p className="mt-1 text-sm text-red-600">{errors.title}</p>
              )}
            </div>

            {/* Body1 - Description */}
            <div>
              <label htmlFor="body1" className="block text-sm font-medium text-gray-700 mb-2">
                Island Description *
              </label>
              <div className={`${errors.body1 ? 'border-red-500' : ''}`}>
                <TextEditor
                  theme="snow"
                  value={formData.body1}
                  onChange={(content) => handleQuillChange(content, 'body1')}
                  modules={quillModules}
                  formats={quillFormats}
                  placeholder="Enter island description"
                  style={{ minHeight: '120px' }}
                  className={`border rounded-md ${errors.body1 ? 'border-red-500' : 'border-gray-300'}`}
                />
              </div>
              {errors.body1 && (
                <p className="mt-1 text-sm text-red-600">{errors.body1}</p>
              )}
            </div>

            {/* Image Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Island Image
              </label>

              {/* Image Preview */}
              {imagePreview && (
                <div className="mb-4 relative">
                  <img
                    src={imagePreview}
                    alt="Island Preview"
                    className="w-full h-48 object-cover rounded-md border border-gray-300"
                  />
                  <button
                    type="button"
                    onClick={() => {
                      setImagePreview('');
                      setFormData(prev => ({ ...prev, image: '' }));
                      setImageFile(null);
                    }}
                    className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              )}

              {/* File Input */}
              <div className="flex items-center space-x-4">
                <label className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer">
                  <MdCloudUpload className="mr-2" />
                  Choose Image
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                    className="hidden"
                  />
                </label>

                {imageFile && (
                  <span className="text-sm text-gray-600">
                    {imageFile.name}
                  </span>
                )}
              </div>

              {errors.image && (
                <p className="mt-1 text-sm text-red-600">{errors.image}</p>
              )}

              <p className="mt-1 text-xs text-gray-500">
                Supported formats: JPEG, PNG, GIF, WebP. Max size: 10MB
              </p>
            </div>
          </div>

          {/* Submit Error */}
          {errors.submit && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-red-600 text-sm">{errors.submit}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 flex items-center"
            >
              <MdCancel className="mr-2" />
              Cancel
            </button>

            <button
              type="submit"
              disabled={isLoading || uploading}
              className={`px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center ${
                isLoading || uploading ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <MdSave className="mr-2" />
              {isLoading || uploading ? 'Saving...' : 'Save Island Data'}
            </button>
          </div>
        </form>
      ) : (
        // View Mode
        <div className="space-y-6">
          {/* Title Display */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Island Title
            </label>
            <div className="p-4 bg-gray-50 rounded-md border">
              <div
                dangerouslySetInnerHTML={{ __html: formData.title || 'No title set' }}
                className="text-gray-900"
              />
            </div>
          </div>

          {/* Image Display */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Island Image
            </label>
            <div className="p-4 bg-gray-50 rounded-md border">
              {formData.image ? (
                <img
                  src={formData.image}
                  alt="Island"
                  className="w-full h-48 object-cover rounded-md"
                />
              ) : (
                <p className="text-gray-500">No image set</p>
              )}
            </div>
          </div>

          {/* Description Display */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Island Description
            </label>
            <div className="p-4 bg-gray-50 rounded-md border">
              <div
                dangerouslySetInnerHTML={{ __html: formData.body1 || 'No description set' }}
                className="text-gray-900"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
